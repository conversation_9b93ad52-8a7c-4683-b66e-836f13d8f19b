import { Tode } from '@/types';
interface TodoItemProps {
  tode: Tode;
  deleteTode: (id: number) => void;
  toggleTode: (id: number) => void;
}

function TodoItem({ tode, deleteTode, toggleTode }: TodoItemProps) {
  return (
    <li style={{ textDecoration: tode.completed ? 'line-through' : 'none' }}>
      {tode.text}
      <button onClick={() => toggleTode(tode.id)}>切换</button>
      <button onClick={() => deleteTode(tode.id)}>删除</button>
    </li>
  );
}
export default TodoItem;
