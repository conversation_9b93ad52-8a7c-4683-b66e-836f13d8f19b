function TodoFilter(filterTodes: (filter: string) => void) {
  function filterTodesBtn(filter: string) {
    filterTodes(filter);
  }

  return (
    <div>
      <button onClick={() => filterTodesBtn('all')}>all</button>
      <button onClick={() => filterTodesBtn('active')}>active</button>
      <button onClick={() => filterTodesBtn('completed')}>completed</button>
    </div>
  );
}

export default TodoFilter;
