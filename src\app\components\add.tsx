'use client';

import { useState } from 'react';

interface AddTodeProps {
  addTode: (text: string) => void;
}

function TodoAdd({ addTode }: AddTodeProps) {
  const [text, setText] = useState('');

  const handleAddTode = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (text.trim() === '') return;

    addTode(text);
    setText('');
  };

  return (
    <form onSubmit={handleAddTode}>
      <input
        type="text"
        value={text}
        onChange={e => setText(e.target.value)}
        placeholder="添加新任务..."
      />
      <button type="submit">添加新任务</button>
    </form>
  );
}

export default TodoAdd;
