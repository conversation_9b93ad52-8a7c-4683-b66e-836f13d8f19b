import TodoItem from './item';
import { Tode } from '@/types';

interface TodeListProps {
  todes: Tode[];
  deleteTode: (id: number) => void;
  toggleTode: (id: number) => void;
}

function TodoList({ todes, deleteTode, toggleTode }: TodeListProps) {
  return (
    <ul>
      {todes.map(tode => (
        <TodoItem
          key={tode.id}
          tode={tode}
          deleteTode={deleteTode}
          toggleTode={toggleTode}
        />
      ))}
    </ul>
  );
}

export default TodoList;
